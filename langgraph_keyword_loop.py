from langgraph.graph import StateGraph, END
from typing import TypedDict, Literal

# ---------- Define State ----------
class GraphState(TypedDict):
    user_input: str
    intent: Literal["greeting", "package", "unknown"]
    keyword_found: bool
    retry_count: int

# ---------- Define Mock Nodes ----------
def classifier_node(state: GraphState) -> GraphState:
    user_input = state.get("user_input", "").lower()
    if "hello" in user_input or "hi" in user_input:
        intent = "greeting"
    elif "package" in user_input or "langgraph" in user_input:
        intent = "package"
    else:
        intent = "unknown"
    print(f"[Classifier] Detected intent: {intent}")
    return {**state, "intent": intent}

def greeting_node(state: GraphState) -> GraphState:
    print("[Greeting Node] Hello there! 👋")
    return state

def package_node(state: GraphState) -> GraphState:
    user_input = state.get("user_input", "")
    keyword_found = "langgraph" in user_input.lower()
    if keyword_found:
        print("[Package Node] Found Keyword !!!!")
    else:
        print("[Package Node] Missing keyword. Asking user again.")
    return {**state, "keyword_found": keyword_found}

def human_node(state: GraphState) -> GraphState:
    retry_count = state.get("retry_count", 0) + 1
    if retry_count >= 5:
        print("[Human Node] Maximum retries reached. Exiting.")
        return {**state, "retry_count": retry_count, "keyword_found": False}
    user_input = input("คุณต้องการอะไร: ")
    return {**state, "user_input": user_input, "retry_count": retry_count}

# ---------- Build the Graph ----------
graph = StateGraph(GraphState)

# Entry point: classify intent
graph.add_node("classifier", classifier_node)
graph.set_entry_point("classifier")

# Add branches
graph.add_node("greeting", greeting_node)
graph.add_node("package", package_node)
graph.add_node("ask_human", human_node)

# Classifier routing logic
graph.add_conditional_edges(
    "classifier",
    lambda state: state["intent"],
    {
        "greeting": "greeting",
        "package": "package",
        "unknown": "ask_human"
    }
)

# After greeting, stop
graph.add_edge("greeting", END)

# After asking human, re-classify input
graph.add_edge("ask_human", "classifier")

# Loop inside package node until keyword is found or retries exhausted
graph.add_conditional_edges(
    "package",
    lambda state: "end" if state["keyword_found"] or state.get("retry_count", 0) >= 5 else "ask",
    {
        "end": END,
        "ask": "ask_human"
    }
)

# Compile graph
# app = graph.compile()
app = graph.compile(interrupt_before=["ask_human"])

# ---------- Entry point for Studio ----------
def run(input: dict):
    return app.invoke(input)

# ---------- Run Example ----------
if __name__ == "__main__":
    user_input = input("User: ")
    app.invoke({"user_input": user_input, "intent": "unknown", "keyword_found": False, "retry_count": 0})
