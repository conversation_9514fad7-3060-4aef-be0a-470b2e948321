from IPython.display import Image, display

# https://www.youtube.com/watch?v=YmAaKKlDy7k
from typing import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from IPython.display import Image, display
class State(TypedDict):
    input: str
    user_feedback: str
    
def step_1(state) :
    print ("---Step 1---")
    pass

def step_2(state):
    print("---human_feedback--_")
    pass

def step_3(state):
    print("---Step 3---")
    pass

builder = StateGraph (State)
builder.add_node("step_1", step_1)
builder.add_node ("human_feedback", step_2)
builder.add_node("step_3", step_3)
builder.add_edge(START, "step_1")
builder.add_edge ("step_1", "human_feedback")
builder.add_edge ("human_feedback", "step_3")
builder.add_edge("step_3", END)

# Set up memory
memory = MemorySaver()
# Add
graph = builder. compile(checkpointer=memory, interrupt_before= ["human_feedback"])
# View
display(Image(graph.get_graph().draw_mermaid_png()))

display(Image(graph.get_graph(xray=True).draw_mermaid_png()))

from typing_extensions import TypedDict
from langgraph.graph.state import StateGraph, START

class State(TypedDict):
    foo: str

# Subgraph

def subgraph_node_1(state: State):
    return {"foo": "hi! " + state["foo"]}

subgraph_builder = StateGraph(State)
subgraph_builder.add_node(subgraph_node_1)
subgraph_builder.add_edge(START, "subgraph_node_1")
subgraph = subgraph_builder.compile()

# Parent graph

builder = StateGraph(State)
builder.add_node("node_1", subgraph)
builder.add_edge(START, "node_1")
graph = builder.compile()

display(Image(graph.get_graph().draw_mermaid_png()))

from typing_extensions import TypedDict
from langgraph.graph.state import StateGraph, START

# Define subgraph
class SubgraphState(TypedDict):
    # note that none of these keys are shared with the parent graph state
    bar: str
    baz: str

def subgraph_node_1(state: SubgraphState):
    return {"baz": "baz"}

def subgraph_node_2(state: SubgraphState):
    return {"bar": state["bar"] + state["baz"]}

subgraph_builder = StateGraph(SubgraphState)
subgraph_builder.add_node(subgraph_node_1)
subgraph_builder.add_node(subgraph_node_2)
subgraph_builder.add_edge(START, "subgraph_node_1")
subgraph_builder.add_edge("subgraph_node_1", "subgraph_node_2")
subgraph = subgraph_builder.compile()

# Define parent graph
class ParentState(TypedDict):
    foo: str

def node_1(state: ParentState):
    return {"foo": "hi! " + state["foo"]}

def node_2(state: ParentState):
    response = subgraph.invoke({"bar": state["foo"]})  
    return {"foo": response["bar"]}  


builder = StateGraph(ParentState)
builder.add_node("node_1", node_1)
builder.add_node("node_2", node_2)
builder.add_edge(START, "node_1")
builder.add_edge("node_1", "node_2")
graph = builder.compile()

for chunk in graph.stream({"foo": "foo"}, subgraphs=True):
    print(chunk)
    
display(Image(graph.get_graph().draw_mermaid_png()))

# Grandchild graph
from typing_extensions import TypedDict
from langgraph.graph.state import StateGraph, START, END

class GrandChildState(TypedDict):
    my_grandchild_key: str

def grandchild_1(state: GrandChildState) -> GrandChildState:
    # NOTE: child or parent keys will not be accessible here
    return {"my_grandchild_key": state["my_grandchild_key"] + ", how are you"}


grandchild = StateGraph(GrandChildState)
grandchild.add_node("grandchild_1", grandchild_1)

grandchild.add_edge(START, "grandchild_1")
grandchild.add_edge("grandchild_1", END)

grandchild_graph = grandchild.compile()

# Child graph
class ChildState(TypedDict):
    my_child_key: str

def call_grandchild_graph(state: ChildState) -> ChildState:
    # NOTE: parent or grandchild keys won't be accessible here
    grandchild_graph_input = {"my_grandchild_key": state["my_child_key"]}  
    grandchild_graph_output = grandchild_graph.invoke(grandchild_graph_input)
    return {"my_child_key": grandchild_graph_output["my_grandchild_key"] + " today?"}  

child = StateGraph(ChildState)
child.add_node("child_1", call_grandchild_graph)  
child.add_edge(START, "child_1")
child.add_edge("child_1", END)
child_graph = child.compile()

# Parent graph
class ParentState(TypedDict):
    my_key: str

def parent_1(state: ParentState) -> ParentState:
    # NOTE: child or grandchild keys won't be accessible here
    return {"my_key": "hi " + state["my_key"]}

def parent_2(state: ParentState) -> ParentState:
    return {"my_key": state["my_key"] + " bye!"}

def call_child_graph(state: ParentState) -> ParentState:
    child_graph_input = {"my_child_key": state["my_key"]}  
    child_graph_output = child_graph.invoke(child_graph_input)
    return {"my_key": child_graph_output["my_child_key"]}  

parent = StateGraph(ParentState)
parent.add_node("parent_1", parent_1)
parent.add_node("child", call_child_graph)  
parent.add_node("parent_2", parent_2)

parent.add_edge(START, "parent_1")
parent.add_edge("parent_1", "child")
parent.add_edge("child", "parent_2")
parent.add_edge("parent_2", END)

parent_graph = parent.compile()

for chunk in parent_graph.stream({"my_key": "Bob"}, subgraphs=True):
    print(chunk)
    
display(Image(parent_graph.get_graph().draw_mermaid_png()))

display(Image(parent_graph.get_graph(xray = True).draw_mermaid_png()))

from langgraph.graph import START, StateGraph
from langgraph.checkpoint.memory import InMemorySaver
from typing_extensions import TypedDict

class State(TypedDict):
    foo: str

# Subgraph

def subgraph_node_1(state: State):
    return {"foo": state["foo"] + "bar"}

subgraph_builder = StateGraph(State)
subgraph_builder.add_node(subgraph_node_1)
subgraph_builder.add_edge(START, "subgraph_node_1")
subgraph = subgraph_builder.compile()

# Parent graph

builder = StateGraph(State)
builder.add_node("node_1", subgraph)
builder.add_edge(START, "node_1")

checkpointer = InMemorySaver()
graph = builder.compile(checkpointer=checkpointer)

display(Image(graph.get_graph().draw_mermaid_png()))

from typing_extensions import TypedDict
from langgraph.graph.state import StateGraph, START

# Define subgraph
class SubgraphState(TypedDict):
    foo: str  
    bar: str  

def subgraph_node_1(state: SubgraphState):
    return {"bar": "bar"}

def subgraph_node_2(state: SubgraphState):
    # note that this node is using a state key ('bar') that is only available in the subgraph
    # and is sending update on the shared state key ('foo')
    return {"foo": state["foo"] + state["bar"]}

subgraph_builder = StateGraph(SubgraphState)
subgraph_builder.add_node(subgraph_node_1)
subgraph_builder.add_node(subgraph_node_2)
subgraph_builder.add_edge(START, "subgraph_node_1")
subgraph_builder.add_edge("subgraph_node_1", "subgraph_node_2")
subgraph = subgraph_builder.compile()

display(Image(subgraph.get_graph().draw_mermaid_png()))

# Define parent graph
class ParentState(TypedDict):
    foo: str

def node_1(state: ParentState):
    return {"foo": "hi! " + state["foo"]}

builder = StateGraph(ParentState)
builder.add_node("node_1", node_1)
builder.add_node("node_2", subgraph)
builder.add_edge(START, "node_1")
builder.add_edge("node_1", "node_2")
graph = builder.compile()

for chunk in graph.stream({"foo": "foo"}):
    print(chunk)

display(Image(graph.get_graph().draw_mermaid_png()))

display(Image(subgraph.get_graph().draw_mermaid_png()))

from langgraph.graph import StateGraph, START
from typing_extensions import TypedDict

class State(TypedDict):
    foo: str

# Define subgraph
def subgraph_node(state: State):
    return {"foo": "Hello, " + state["foo"]}

subgraph_builder = StateGraph(State)
subgraph_builder.add_node("subgraph_node", subgraph_node)
subgraph_builder.add_edge(START, "subgraph_node")
subgraph = subgraph_builder.compile()

# Define parent graph
builder = StateGraph(State)
builder.add_node("parent_node", subgraph)
builder.add_edge(START, "parent_node")
graph = builder.compile()

display(Image(graph.get_graph().draw_mermaid_png()))

from langgraph.graph import StateGraph, END, START
from typing_extensions import TypedDict

# --- Shared state for simplicity ---
class State(TypedDict):
    intent: str
    slots: dict
    query: str


# === Subgraph: flow_package_start ===
def await_input(state: State) -> State:
    print("Waiting for user input...")
    return state

def extract_slots(state: State) -> State:
    print("Extracting slots...")
    state["slots"] = {"slot1": "value1"}
    return state

def build_query(state: State) -> State:
    print("Building query from slots...")
    state["query"] = f"SELECT * FROM packages WHERE slot1='{state['slots']['slot1']}'"
    return state

subgraph_builder = StateGraph(State)
subgraph_builder.add_node("await_input", await_input)
subgraph_builder.add_node("extract_slots", extract_slots)
subgraph_builder.add_node("build_query", build_query)
subgraph_builder.set_entry_point("await_input")
subgraph_builder.add_edge("await_input", "extract_slots")
subgraph_builder.add_edge("extract_slots", "build_query")
subgraph = subgraph_builder.compile()


# === Main Nodes ===
def classify_intent(state: State) -> State:
    print("Classifying intent...")
    # mock intent for testing purposes
    state["intent"] = "booking"  # try different values to simulate routing
    return state

def flow_booking_start(state: State) -> State:
    print("Booking flow...")
    return state

def flow_greeting_start(state: State) -> State:
    print("Greeting flow...")
    return state

def flow_info_start(state: State) -> State:
    print("Info flow...")
    return state

def flow_unknown_start(state: State) -> State:
    print("Unknown flow...")
    return state

def out_of_scope(state: State) -> State:
    print("Out of scope...")
    return state


# === Main Graph ===
graph = StateGraph(State)
graph.add_node("classify_intent", classify_intent)

graph.add_node("flow_booking_start", flow_booking_start)
graph.add_node("flow_greeting_start", flow_greeting_start)
graph.add_node("flow_info_start", flow_info_start)
graph.add_node("flow_unknown_start", flow_unknown_start)
graph.add_node("out_of_scope", out_of_scope)

# Add subgraph directly
graph.add_node("flow_package_start", subgraph)

# Define transitions from classify_intent
graph.add_edge("classify_intent", "flow_booking_start")
graph.add_edge("classify_intent", "flow_greeting_start")
graph.add_edge("classify_intent", "flow_info_start")
graph.add_edge("classify_intent", "flow_package_start")
graph.add_edge("classify_intent", "flow_unknown_start")

# Terminal transitions
graph.add_edge("flow_booking_start", END)
graph.add_edge("flow_greeting_start", END)
graph.add_edge("flow_info_start", END)
graph.add_edge("flow_package_start", END)
graph.add_edge("flow_unknown_start", "out_of_scope")
graph.add_edge("out_of_scope", END)

graph.set_entry_point("classify_intent")
compiled_graph = graph.compile()


display(Image(subgraph.get_graph().draw_mermaid_png()))

display(Image(compiled_graph.get_graph().draw_mermaid_png()))

display(Image(compiled_graph.get_graph(xray=True).draw_mermaid_png()))


# https://langchain-ai.github.io/langgraphjs/how-tos/subgraphs-manage-state/#define-parent-graph

from typing import List, TypedDict, Literal
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END, START
from langgraph.checkpoint.memory import MemorySaver
from pydantic import BaseModel

# === Messages and States ===
class Message(TypedDict):
    role: str
    content: str

class SubGraphState(TypedDict):
    messages: List[Message]
    city: str

class RouterState(TypedDict):
    messages: List[Message]
    route: Literal["weather", "other"]

# === Tool ===
@tool
def get_weather(city: str) -> str:
    """Get the weather for a specific city."""
    return f"It's sunny in {city}"


# === Models ===
raw_model = ChatOpenAI(model="gpt-4o-mini", api_key = "********************************************************************************************************************************************************************")
model_with_weather = raw_model.with_structured_output(get_weather)

# === Subgraph ===
async def model_node(state: SubGraphState) -> dict:
    result = await model_with_weather.ainvoke(state["messages"])
    return {"city": result["city"]}

async def weather_node(state: SubGraphState) -> dict:
    result = await get_weather.ainvoke({"city": state["city"]})
    return {
        "messages": [
            {"role": "assistant", "content": result}
        ]
    }

subgraph = (
    StateGraph(SubGraphState)
    .add_node("modelNode", model_node)
    .add_node("weatherNode", weather_node)
    .add_edge(START, "modelNode")
    .add_edge("modelNode", "weatherNode")
    .add_edge("weatherNode", END)
    .compile(interrupt_before=["weatherNode"])
)

# === Router Model ===
class RouteOutput(BaseModel):
    route: Literal["weather", "other"]

router_model = raw_model.with_structured_output(RouteOutput)

async def router_node(state: RouterState) -> dict:
    system = {"role": "system", "content": "Classify the incoming query as either about weather or not."}
    result = await router_model.ainvoke([system, *state["messages"]])
    return {"route": result.route}

async def normal_llm_node(state: RouterState) -> dict:
    response = await raw_model.ainvoke(state["messages"])
    return {"messages": [response]}

async def route_after_prediction(state: RouterState) -> str:
    return "weatherGraph" if state["route"] == "weather" else "normalLLMNode"

# === Parent Graph ===
memory = MemorySaver()

graph = (
    StateGraph(RouterState)
    .add_node("routerNode", router_node)
    .add_node("normalLLMNode", normal_llm_node)
    .add_node("weatherGraph", subgraph)
    .add_edge(START, "routerNode")
    .add_conditional_edges("routerNode", route_after_prediction)
    .add_edge("normalLLMNode", END)
    .add_edge("weatherGraph", END)
    .compile(checkpointer=memory)
)


display(Image(graph.get_graph().draw_mermaid_png()))

display(Image(graph.get_graph(xray=1).draw_mermaid_png()))


import logging

class State(TypedDict):
    input: str
    user_feedback: str
    
def step_1(state) :
    print ("---Step 1---")
    pass

def step_2(state):
    print("---human_feedback--_")
    pass

def step_3(state):
    print("---Step 3---")
    pass

def step_4(state):
    print("---Step 4---")
    pass

def step_5(state):
    print("---Step 5---")
    pass

def step_6(state):
    print("---Step 6---")
    pass

# Conditional routing functions
def should_continue_extraction(state) -> str:
    """Determine if we should continue extracting or proceed to build query"""
    missing_fields = state.get("missing_fields", [])
    iteration_count = state.get("iteration_count", 0)

    # Prevent infinite loops - after 5 iterations, proceed anyway
    if iteration_count > 5:
        # logger.warning("Maximum iterations reached, proceeding to build query")
        return "build_query"

    # Only proceed to build query if ALL 4 fields are captured
    if not missing_fields:
        # logger.info("All 4 required fields captured, proceeding to build query")
        return "build_query"
    else:
        # logger.info(f"Missing fields: {missing_fields}, continuing extraction")
        return "continue"

def should_retry_query(state) -> str:
    """Determine if we should retry query generation or proceed"""
    generated_sql = state.get("generated_sql", "")

    # Simple validation - check if SQL was generated
    if not generated_sql or "SELECT" not in generated_sql.upper():
        return "retry"
    else:
        return "proceed"
    
# Define the subgraph
workflow = StateGraph(State)

# Add nodes
workflow.add_node("await_input", step_1)
workflow.add_node("extract_slots", step_2)
workflow.add_node("check_completion", step_3)
workflow.add_node("build_query", step_4)
workflow.add_node("validate_query", step_5)
workflow.add_node("final_answer", step_5)

# Set entry point
workflow.set_entry_point("await_input")

# Add edges with conditional routing
workflow.add_edge("await_input", "extract_slots")
workflow.add_edge("extract_slots", "check_completion")

# Conditional edge: if all fields captured, go to build_query, else loop back to await_input
workflow.add_conditional_edges(
    "check_completion",
    should_continue_extraction,
    {
        "continue": "await_input",
        "build_query": "build_query"
    }
)

workflow.add_edge("build_query", "validate_query")
workflow.add_conditional_edges(
    "validate_query",
    should_retry_query,
    {
        "retry": "build_query",
        "proceed": "final_answer"
    }
)
workflow.add_edge("final_answer", END)

graph = workflow.compile()

display(Image(graph.get_graph().draw_mermaid_png()))

from typing_extensions import TypedDict
from langgraph.graph.state import StateGraph, START

# Define subgraph
class SubgraphState(TypedDict):
    foo: str  
    bar: str  

def subgraph_node_1(state: SubgraphState):
    return {"bar": "bar"}

def subgraph_node_2(state: SubgraphState):
    # note that this node is using a state key ('bar') that is only available in the subgraph
    # and is sending update on the shared state key ('foo')
    return {"foo": state["foo"] + state["bar"]}

subgraph_builder = StateGraph(SubgraphState)
subgraph_builder.add_node(subgraph_node_1)
subgraph_builder.add_node(subgraph_node_2)
subgraph_builder.add_edge(START, "subgraph_node_1")
subgraph_builder.add_edge("subgraph_node_1", "subgraph_node_2")
subgraph = subgraph_builder.compile()

# Define parent graph
class ParentState(TypedDict):
    foo: str

def node_1(state: ParentState):
    return {"foo": "hi! " + state["foo"]}

builder = StateGraph(ParentState)
builder.add_node("node_1", node_1)
builder.add_node("node_2", subgraph)
builder.add_edge(START, "node_1")
builder.add_edge("node_1", "node_2")
graph = builder.compile()

for chunk in graph.stream({"foo": "foo"}):
    print(chunk)
    
display(Image(graph.get_graph().draw_mermaid_png()))
    
display(Image(graph.get_graph(xray=1).draw_mermaid_png()))


